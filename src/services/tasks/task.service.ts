import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {PlannerCampaignRepository, TaskRepository, EmailGenerationRepository, UnlayerComponentRepository, PromptTemplateRepository, OrganizationPlannerPlanRepository, PlannerPlanVersionRepository, OrganizationRepository, OrganizationSegmentRepository, CampaignSegmentRepository} from '../../repositories';
import {PlannerCampaign, Task, TaskStep, TaskWithRelations, TaskStepWithRelations} from '../../models';
import {LLMRouterService} from '../chat/llm-router.service';
import {CompletionMessage, RouterParams, RouterResponse} from '../chat/types';
import {KlaviyoService} from '../integrations/klaviyo.service';
import {PromptContextService} from '../prompt/prompt-context.service';
import {PromptLogService} from '../prompt/prompt-log.service';
import {setNestedValue} from '../../utils/object-utils';
import {jsonrepair} from 'jsonrepair';
import {handleClaudeResponse} from '../chat/utils/campaign-parser';


@injectable({scope: BindingScope.TRANSIENT})
export class TaskService {
	constructor(
		@repository(PlannerCampaignRepository) private plannerCampaignRepository: PlannerCampaignRepository,
		@repository(TaskRepository) private taskRepository: TaskRepository,
		@repository(EmailGenerationRepository) private emailGenerationRepository: EmailGenerationRepository,
		@repository(UnlayerComponentRepository) private unlayerComponentRepository: UnlayerComponentRepository,
		@repository(PromptTemplateRepository) private promptTemplateRepository: PromptTemplateRepository,
		@repository(OrganizationPlannerPlanRepository) private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
		@repository(PlannerPlanVersionRepository) private plannerPlanVersionRepository: PlannerPlanVersionRepository,
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
		@repository(OrganizationSegmentRepository) private organizationSegmentRepository: OrganizationSegmentRepository,
		@repository(CampaignSegmentRepository) private campaignSegmentRepository: CampaignSegmentRepository,
		@service(LLMRouterService) private llmRouter: LLMRouterService,
		@service(KlaviyoService) private klaviyoService: KlaviyoService,
		@service(PromptContextService) private promptContextService: PromptContextService,
		@service(PromptLogService) private promptLogService: PromptLogService,
	) { }

	async createTaskForPlannerCampaign(plannerCampaign: PlannerCampaign): Promise<Task> {
		const plannerCampaignTemplate = await this.findTemplateForPlannerCampaign(plannerCampaign);

		const taskSteps = plannerCampaignTemplate.task.taskSteps.map((step) => ({
			...step,
			id: undefined,
			taskId: undefined
		}));

		const templateTask = plannerCampaignTemplate.task;
		delete templateTask.id;
		templateTask.taskSteps = undefined as any;
		delete (templateTask as any).taskSteps;


		const task = await this.taskRepository.create({
			...templateTask,
			plannerCampaignId: plannerCampaign.id,
			name: plannerCampaign.name,
			description: plannerCampaign.description,
			isTemplate: false,
			version: "0.9.2"
		});

		for (const step of taskSteps) {
			delete step.taskId;
			delete step.id;
			await this.taskRepository.taskSteps(task.id).create(step);
		}

		return task;
	}

	private async findTemplateForPlannerCampaign(plannerCampaign: PlannerCampaign): Promise<PlannerCampaign> {
		const matchingTemplate = await this.plannerCampaignRepository.findOne({
			where: {
				taskType: plannerCampaign.taskType,
				isTemplate: true
			},
			include: [
				{
					relation: 'task',
					scope: {
						include: [
							{
								relation: 'taskSteps'
							}
						]
					}
				}
			]
		});

		if (!matchingTemplate) {
			throw new Error('No matching task template found for planner campaign');
		}

		return matchingTemplate;
	}

	async postProcessEmailContent(task: TaskWithRelations, userPrompt?: string, templateType: string = 'Brief'): Promise<void> {
		// Get task with its steps
		const taskWithSteps = await this.taskRepository.findById(task.id, {
			include: [{
				relation: 'taskSteps'
			}]
		});

		// Find Content TaskStep if it exists
		const contentStep = (taskWithSteps.taskSteps as TaskStepWithRelations[])?.find(
			(step) => {
				const taskType = step?.name;
				return taskType === 'Content';
			}
		);

		if (contentStep) {
			// Update task status to Processing
			await this.taskRepository.updateById(task.id, {
				status: 'Processing'
			});

			const maxRetries = 5; // Maximum number of retries
			const initialDelay = 1000; // Initial delay in milliseconds (1 second)
			let retryCount = 0;

			// Get organization data for the email
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);


			while (retryCount < maxRetries) {
				const promptTemplate = await this.promptTemplateRepository.findOne({
					where: {
						type: templateType,
						isActive: true
					}
				});

				if (!promptTemplate) {
					throw new Error(`No active prompt template found for "${templateType}"`);
				}

				if (!plan.organizationId) {
					throw new Error('Organization ID is required');
				}

				const prompt = await this.promptContextService.replacePromptTags(promptTemplate.content, plan.organizationId, userPrompt, campaign, taskWithSteps);

				try {
					const messages: CompletionMessage[] = [{role: 'user', content: prompt}];
					const params: RouterParams = {
						models: ["openai/chatgpt-4o-latest", "openai/o3-mini"],
						maxTokens: 4096
					};
					const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
					const completion = response.content;

					// Store completion in Content TaskStep's data field
					await this.taskRepository.taskSteps(task.id).patch({
						data: completion
					}, {
						id: contentStep.id
					});

					await this.promptLogService.logPromptData(plan.organizationId, templateType, prompt, completion);

					// Update task status to Ready
					await this.taskRepository.updateById(task.id, {
						status: 'Ready'
					});

					// If successful, break out of retry loop
					break;

				} catch (error: any) {
					retryCount++;
					if (retryCount >= maxRetries) {
						console.error('Failed to get AI completion after maximum retries:', error);
						// Don't throw error, continue with other tasks
						break;
					}

					// Calculate exponential backoff delay
					const delay = initialDelay * Math.pow(2, retryCount - 1);
					console.warn(`Attempt ${retryCount} failed. Retrying in ${delay}ms...`);

					// Wait before retrying
					await new Promise(resolve => setTimeout(resolve, delay));
				}
			}
		}
	}

	async createUnlayerEmail(task: TaskWithRelations, templateType: string = 'GeneratedEmail'): Promise<any> {
		// Get related data
		const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
		const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
		const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

		const promptTemplate = await this.promptTemplateRepository.findOne({
			where: {
				type: templateType,
				isActive: true
			}
		});

		if (!promptTemplate) {
			throw new Error(`No active prompt template found for "${templateType}"`);
		}

		if (!plan.organizationId) {
			throw new Error('Organization ID is required');
		}
		console.log('promptTemplate', promptTemplate);
		const prompt = await this.promptContextService.replacePromptTags(promptTemplate.content, plan.organizationId, undefined, campaign, task);

		try {
			// Get completion from LLM Router
			const messages: CompletionMessage[] = [{role: 'user', content: prompt}];
			const params: RouterParams = {
				models: ["anthropic/claude-3.5-sonnet", "openai/o3-mini"],
				maxTokens: 4096
			};
			const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
			const completion = response.content;

			await this.promptLogService.logPromptData(plan.organizationId, templateType, prompt, completion);

			// Parse the JSON response
			let cleaned = completion.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
			const firstBrace = cleaned.indexOf('{');
			const lastBrace = cleaned.lastIndexOf('}');
			if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
				cleaned = cleaned.substring(firstBrace, lastBrace + 1);
			}
			const repairedJsonString = jsonrepair(cleaned);
			const emailDesign = JSON.parse(repairedJsonString);

			//Find the EmailBase Component either overriden or not
			let emailBaseComponent = await this.unlayerComponentRepository.findOne({
				where: {
					name: 'EmailBase',
					orgId: plan.organizationId
				}
			});

			if (!emailBaseComponent) {
				emailBaseComponent = await this.unlayerComponentRepository.findOne({
					where: {
						name: 'EmailBase',
						orgId: {eq: null as any}
					}
				});
			}
			let emailComponents = []
			//For each item in emailDesign we need to look for the component and append
			for (const item of emailDesign.components) {
				let component = await this.unlayerComponentRepository.findOne({
					where: {
						name: item.name,
						orgId: plan.organizationId
					}
				});

				if (!component) {
					component = await this.unlayerComponentRepository.findOne({
						where: {
							name: item.name,
							orgId: {eq: null as any}
						}
					});
				}
				emailComponents.push(component);
			}

			//Lets assemble the email
			let emailJSON = emailBaseComponent?.json;
			let emailJSONFinal = emailJSON ? JSON.parse(emailJSON) : null;
			//Go through each component and add it to the emailJSON
			for (var i = 0; i < emailComponents.length; i++) {
				let emailBrandingComponent = emailDesign.components[i];
				let componentJSON = emailComponents[i]?.json;
				let componentObject = componentJSON ? JSON.parse(componentJSON) : null;


				//lets see if we have any editable fields
				if (emailBrandingComponent?.fields && emailBrandingComponent?.fields.length > 0) {
					for (const field of emailBrandingComponent.fields) {
						if (componentObject) {
							// Replace direct assignment with nested setter
							setNestedValue(componentObject, field.name, field.value);
						}
					}
				}

				//Add all body.rows to the emailJSONFinal body.rows array
				if (!emailJSONFinal.body.rows) {
					emailJSONFinal.body.rows = [];
				}

				if (componentObject?.body?.rows) {
					emailJSONFinal.body.rows = emailJSONFinal.body.rows.concat(componentObject.body.rows);
				}
			}

			// Validate the structure (basic validation)
			if (!emailJSONFinal.body || !emailJSONFinal.body.rows || !Array.isArray(emailJSONFinal.body.rows)) {
				console.error('Invalid email design structure:', emailJSONFinal);
				console.error('Retrying...');
				return this.createUnlayerEmail(task);
				//throw new Error('Invalid email design structure');
			}

			// Store the design in the task's data
			await this.taskRepository.updateById(task.id, {
				emailDesign: emailJSONFinal,
				status: 'In Progress'
			});

			return emailDesign;
		} catch (error: any) {
			console.error('Failed to create email design:', error);
			console.error('Retrying...');
			return this.createUnlayerEmail(task);
			//throw new Error('Failed to generate email design: ' + error.message);
		}
	}

	private convertToHybridTemplate(htmlContent: string): string {
		// Parse HTML using Cheerio (server-side jQuery) and convert text and image content to Klaviyo hybrid blocks
		// Focus on table cell content as per Klaviyo documentation

		const cheerio = require('cheerio');
		const $ = cheerio.load(htmlContent);

		// Debug logging - count total images before processing
		const totalImages = $('img').length;
		const totalLinks = $('a').length;
		const totalTds = $('td').length;
		console.log(`[Hybrid Conversion] Starting conversion with ${totalImages} total images, ${totalLinks} links, ${totalTds} table cells`);

		// Helper function to get all attributes as a string
		const getAttributesString = (element: any): string => {
			const attrs = element.attribs || {};
			return Object.keys(attrs).map(key => ` ${key}="${attrs[key]}"`).join('');
		};

		// Helper function to get image attributes with size constraints preserved
		const getImageAttributesWithConstraints = (element: any): string => {
			const attrs = element.attribs || {};
			const $element = $(element);

			// Get computed styles from parent table structure to preserve sizing
			const $parentTd = $element.closest('td');
			const $parentTable = $element.closest('table');

			// Build attributes string, ensuring critical sizing attributes are preserved
			let attributeString = '';

			// Always preserve these critical attributes for proper sizing
			const criticalAttrs = ['src', 'alt', 'width', 'height', 'style'];

			// Add all existing attributes
			Object.keys(attrs).forEach(key => {
				attributeString += ` ${key}="${attrs[key]}"`;
			});

			// If no explicit width/height, try to infer from parent table structure
			if (!attrs.width && !attrs.height) {
				// Check if parent td has width constraints
				const parentTdStyle = $parentTd.attr('style') || '';
				const parentTdWidth = $parentTd.attr('width');

				if (parentTdWidth) {
					// If parent td has width, constrain image to fit
					if (!attributeString.includes('width=')) {
						attributeString += ` width="${parentTdWidth}"`;
					}
				} else if (parentTdStyle.includes('width:')) {
					// Extract width from parent td style
					const widthMatch = parentTdStyle.match(/width:\s*([^;]+)/);
					if (widthMatch && !attributeString.includes('width=')) {
						const width = widthMatch[1].trim();
						// Convert percentage or other units to pixels if possible
						if (width.includes('%')) {
							// For percentage widths, add max-width constraint
							const currentStyle = attrs.style || '';
							const maxWidthStyle = currentStyle.includes('max-width') ? currentStyle :
								currentStyle + (currentStyle ? '; ' : '') + `max-width: ${width === '100%' ? '200px' : '150px'}`;
							attributeString = attributeString.replace(/style="[^"]*"/, `style="${maxWidthStyle}"`);
							if (!attributeString.includes('style=')) {
								attributeString += ` style="${maxWidthStyle}"`;
							}
						} else {
							attributeString += ` width="${width}"`;
						}
					}
				}
			}

			// Ensure images have max-width constraint to prevent overflow
			const currentStyle = attrs.style || '';
			if (!currentStyle.includes('max-width')) {
				const enhancedStyle = currentStyle + (currentStyle ? '; ' : '') + 'max-width: 100%; height: auto;';
				if (attributeString.includes('style=')) {
					attributeString = attributeString.replace(/style="[^"]*"/, `style="${enhancedStyle}"`);
				} else {
					attributeString += ` style="${enhancedStyle}"`;
				}
			}

			return attributeString;
		};

		// Helper function to detect if images are in a "3 across" or similar row layout
		const isInRowLayout = (img: any): boolean => {
			const $img = $(img);
			const $parentTd = $img.closest('td');
			const $parentTr = $img.closest('tr');

			if ($parentTr.length === 0) return false;

			// Count images in the same row
			const imagesInRow = $parentTr.find('img').length;

			// Check if this looks like a footer or social media row (2-4 images in a row)
			if (imagesInRow >= 2 && imagesInRow <= 4) {
				// Additional check: are the images roughly the same size or in similar table cells?
				const $allTdsInRow = $parentTr.find('td');
				const tdsWithImages = $allTdsInRow.filter(function(this: any) {
					return $(this).find('img').length > 0;
				});

				// If most cells in the row have images, it's likely a row layout
				const isRowLayout = tdsWithImages.length >= 2;

				if (isRowLayout) {
					console.log(`[Row Layout Detection] Found ${imagesInRow} images in row layout for image: ${$img.attr('src')?.substring(0, 30) || 'no-src'}`);
				}

				return isRowLayout;
			}

			return false;
		};

		// Helper function to get constrained image attributes for row layouts
		const getRowLayoutImageAttributes = (img: any): string => {
			const attrs = img.attribs || {};
			const $img = $(img);
			const $parentTr = $img.closest('tr');
			const imagesInRow = $parentTr.find('img').length;

			console.log(`[Row Layout Sizing] Processing image with ${imagesInRow} images in row: ${$img.attr('src')?.substring(0, 30) || 'no-src'}`);

			// Build attributes string
			let attributeString = '';
			Object.keys(attrs).forEach(key => {
				attributeString += ` ${key}="${attrs[key]}"`;
			});

			// For row layouts, ensure consistent sizing
			const currentStyle = attrs.style || '';
			let enhancedStyle = currentStyle;

			// Add constraints based on number of images in row
			if (imagesInRow === 3) {
				// 3 across - limit to reasonable size
				if (!currentStyle.includes('max-width')) {
					enhancedStyle += (enhancedStyle ? '; ' : '') + 'max-width: 120px; height: auto;';
					console.log(`[Row Layout Sizing] Applied 3-across constraints: max-width: 120px`);
				}
			} else if (imagesInRow === 2) {
				// 2 across - slightly larger
				if (!currentStyle.includes('max-width')) {
					enhancedStyle += (enhancedStyle ? '; ' : '') + 'max-width: 150px; height: auto;';
					console.log(`[Row Layout Sizing] Applied 2-across constraints: max-width: 150px`);
				}
			} else if (imagesInRow === 4) {
				// 4 across - smaller
				if (!currentStyle.includes('max-width')) {
					enhancedStyle += (enhancedStyle ? '; ' : '') + 'max-width: 100px; height: auto;';
					console.log(`[Row Layout Sizing] Applied 4-across constraints: max-width: 100px`);
				}
			}

			// Ensure consistent display
			if (!enhancedStyle.includes('display:')) {
				enhancedStyle += (enhancedStyle ? '; ' : '') + 'display: inline-block; vertical-align: middle;';
			}

			// Update or add style attribute
			if (attributeString.includes('style=')) {
				attributeString = attributeString.replace(/style="[^"]*"/, `style="${enhancedStyle}"`);
			} else {
				attributeString += ` style="${enhancedStyle}"`;
			}

			console.log(`[Row Layout Sizing] Final enhanced style: ${enhancedStyle}`);
			return attributeString;
		};

		// Helper function to check if element already has klaviyo-region
		const hasKlaviyoRegion = (element: any): boolean => {
			return element.attribs && element.attribs['data-klaviyo-region'];
		};

		// Helper function to add align="center" if not present
		const ensureAlignCenter = (element: any): void => {
			if (!element.attribs) element.attribs = {};
			if (!element.attribs.align) {
				element.attribs.align = 'center';
			}
		};

		// Helper function to get appropriate region width based on row layout
		const getRegionWidth = (img: any): string => {
			const $img = $(img);
			const $parentTr = $img.closest('tr');

			if ($parentTr.length === 0) return '600'; // Default full width

			// Count images in the same row
			const imagesInRow = $parentTr.find('img').length;

			// Calculate appropriate width based on number of images
			if (imagesInRow >= 4) {
				console.log(`[Region Width] 4+ images in row, setting width to 150px`);
				return '150'; // 4+ across
			}
			if (imagesInRow === 3) {
				console.log(`[Region Width] 3 images in row, setting width to 200px`);
				return '200'; // 3 across
			}
			if (imagesInRow === 2) {
				console.log(`[Region Width] 2 images in row, setting width to 300px`);
				return '300'; // 2 across
			}

			console.log(`[Region Width] Single image, setting width to 600px`);
			return '600'; // Single image, full width
		};

		// Helper function to get text content without HTML tags
		const getTextContent = (element: any): string => {
			return $(element).text().trim();
		};

		// Helper function to check if element contains images
		const containsImages = (element: any): boolean => {
			return $(element).find('img').length > 0;
		};

		// Helper function to check if element contains links
		const containsLinks = (element: any): boolean => {
			return $(element).find('a').length > 0;
		};

		// Pattern 1: Target text content in padding cells (td with padding containing div)
		$('td[style*="padding"]').each((_i: any, td: any) => {
			if (hasKlaviyoRegion(td)) return;

			const $td = $(td);
			const $div = $td.children('div').first();
			if ($div.length === 0) return;

			const textContent = getTextContent($div[0]);
			const hasImage = containsImages($div[0]);
			const hasSubstantialText = textContent.length > 25;

			if (hasSubstantialText && !hasImage) {
				ensureAlignCenter(td);
				$td.attr('data-klaviyo-region', 'true');
				$td.attr('data-klaviyo-region-width-pixels', '600');

				const divAttributes = getAttributesString($div[0]);
				const divContent = $div.html();

				$td.html(`
					<div class="klaviyo-block klaviyo-text-block">
						<div${divAttributes}>${divContent}</div>
					</div>
				`);
			}
		});

		// Pattern 2: Target standalone image cells (simple image in td)
		$('td').each((_i: any, td: any) => {
			if (hasKlaviyoRegion(td)) return;

			const $td = $(td);
			const $images = $td.find('img');

			// Check if td contains only one img element (ignoring whitespace and text nodes)
			if ($images.length === 1) {
				const $img = $images.first();

				// Skip if this image is inside a link - let Pattern 7 handle linked images
				if ($img.closest('a').length > 0) {
					console.log(`[Pattern 2] Skipping linked image: ${$img.attr('src')?.substring(0, 30) || 'no-src'} - will be handled by Pattern 7`);
					return;
				}

				// Check if this is the primary content (no other substantial elements)
				const $otherElements = $td.children().not('img').filter(function(this: any) {
					const $this = $(this);
					// Ignore empty elements and those with only whitespace
					return $this.text().trim().length > 0 || $this.children().length > 0;
				});

				if ($otherElements.length === 0) {
					ensureAlignCenter(td);
					$td.attr('data-klaviyo-region', 'true');
					$td.attr('data-klaviyo-region-width-pixels', getRegionWidth($img[0]));

					// Check if this image is in a row layout (like 3 across footer)
					const inRowLayout = isInRowLayout($img[0]);
					console.log(`[Pattern 2] Image ${$img.attr('src')?.substring(0, 30) || 'no-src'} - Row layout: ${inRowLayout}`);
					const imgAttributes = inRowLayout ?
						getRowLayoutImageAttributes($img[0]) :
						getImageAttributesWithConstraints($img[0]);

					$td.html(`
						<div class="klaviyo-block klaviyo-image-block">
							<img${imgAttributes}>
						</div>
					`);
				}
			}
		});

		// Pattern 3: Target table-wrapped images (common in email templates)
		// Only process images that haven't been handled by previous patterns
		$('img').each((_i: any, img: any) => {
			const $img = $(img);

			// Skip if this image is inside ANY klaviyo region (prevents nested processing)
			const $anyKlaviyoParent = $img.closest('[data-klaviyo-region="true"]');
			if ($anyKlaviyoParent.length > 0) return;

			// Look for images that are nested in table structures within td elements
			const $parentTd = $img.closest('td');
			if ($parentTd.length === 0 || hasKlaviyoRegion($parentTd[0])) return;

			// Check if this image is wrapped in table structure: td > table > tr > td > img
			const $table = $img.closest('table');
			const $outerTd = $table.parent();

			// Only process if outer td is different from parent td (indicating nested table structure)
			if ($outerTd.length > 0 && $outerTd.is('td') && !hasKlaviyoRegion($outerTd[0]) && $outerTd[0] !== $parentTd[0]) {
				// Verify this is a simple image-only table structure
				const $allImages = $outerTd.find('img');
				const $significantContent = $outerTd.find('*').not('table, tr, td, img').filter(function(this: any) {
					return $(this).text().trim().length > 0;
				});

				// Only convert if this appears to be primarily for image layout
				if ($allImages.length === 1 && $significantContent.length === 0) {
					ensureAlignCenter($outerTd[0]);
					$outerTd.attr('data-klaviyo-region', 'true');
					$outerTd.attr('data-klaviyo-region-width-pixels', getRegionWidth(img));

					const imgAttributes = getImageAttributesWithConstraints(img);
					$outerTd.html(`
						<div class="klaviyo-block klaviyo-image-block">
							<table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="padding-right: 0px;padding-left: 0px;" align="center">
										<img${imgAttributes}>
									</td>
								</tr>
							</table>
						</div>
					`);
				}
			}
		});

		// Pattern 4: Target heading elements (H1, H2, H3, etc.) directly in table cells
		const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
		headingSelectors.forEach(selector => {
			$(`td > ${selector}`).each((_i: any, heading: any) => {
				const $heading = $(heading);
				const $td = $heading.parent();

				if (!$td.is('td') || hasKlaviyoRegion($td[0])) return;

				const textContent = getTextContent(heading);
				if (textContent.length > 3) {
					ensureAlignCenter($td[0]);
					$td.attr('data-klaviyo-region', 'true');
					$td.attr('data-klaviyo-region-width-pixels', '600');

					const headingAttributes = getAttributesString(heading);
					const headingContent = $heading.html();
					const headingTag = heading.tagName.toLowerCase();

					$td.html(`
						<div class="klaviyo-block klaviyo-text-block">
							<${headingTag}${headingAttributes}>${headingContent}</${headingTag}>
						</div>
					`);
				}
			});
		});

		// Pattern 5: Target button/link content (buttons with spans inside links)
		$('td').each((_i: any, td: any) => {
			if (hasKlaviyoRegion(td)) return;

			const $td = $(td);
			// Look for a > span structure that looks like a button
			$td.find('a').each((_j: any, link: any) => {
				const $link = $(link);
				const $spans = $link.find('span');
				if ($spans.length === 0) return;

				// Check if this looks like a button (has span with text inside a link)
				const $span = $spans.first();
				const textContent = getTextContent($span[0]);
				const isLikelyButton = textContent.length > 2 && textContent.length < 50;

				if (isLikelyButton) {
					// Check if this is the main content of the td (not nested deeply)
					const tdTextContent = getTextContent(td);
					const spanTextRatio = textContent.length / Math.max(tdTextContent.length, 1);

					// Only convert if the button text is a significant portion of the cell content
					if (spanTextRatio > 0.5) {
						ensureAlignCenter(td);
						$td.attr('data-klaviyo-region', 'true');
						$td.attr('data-klaviyo-region-width-pixels', '600');

						const linkAttributes = getAttributesString(link);
						const spanAttributes = getAttributesString($span[0]);
						const spanContent = $span.html();

						// Remove MSO comments and create clean button structure
						$td.html(`
							<div class="klaviyo-block klaviyo-text-block">
								<div align="center">
									<a${linkAttributes}>
										<span${spanAttributes}>${spanContent}</span>
									</a>
								</div>
							</div>
						`);
						return false; // Exit early since we've processed this td
					}
				}
			});
		});

		// Pattern 6: Target text content in simple div structures (broader than Pattern 1)
		$('td > div').each((_i: any, div: any) => {
			const $div = $(div);
			const $td = $div.parent();

			if (!$td.is('td') || hasKlaviyoRegion($td[0])) return;

			// Check if this is substantial text content (no images or links)
			const textContent = getTextContent(div);
			const hasImage = containsImages(div);
			const hasLink = containsLinks(div);
			const hasSubstantialText = textContent.length > 8; // Text like "WATER SAFETY FIRST"

			// Only convert if it's text content without complex nested structures
			if (hasSubstantialText && !hasImage && !hasLink) {
				// Check if this div is the main content of the td
				const children = $td.children();
				if (children.length === 1 && children.first().is(div)) {
					ensureAlignCenter($td[0]);
					$td.attr('data-klaviyo-region', 'true');
					$td.attr('data-klaviyo-region-width-pixels', '600');

					const divAttributes = getAttributesString(div);
					const divContent = $div.html();

					$td.html(`
						<div class="klaviyo-block klaviyo-text-block">
							<div${divAttributes}>${divContent}</div>
						</div>
					`);
				}
			}
		});

		// Pattern 6.5: Target 3-across link menus (text links in table cells)
		$('td').each((_i: any, td: any) => {
			if (hasKlaviyoRegion(td)) return;

			const $td = $(td);

			// Look for table cells that contain a single link with short text (menu items)
			const $links = $td.find('a');
			const textContent = getTextContent(td).trim();
			const hasImages = $td.find('img').length > 0;

			// Check if this cell contains exactly one link with short menu-like text
			if ($links.length === 1 && !hasImages && textContent.length > 2 && textContent.length < 50) {
				const $link = $links.first();
				const linkText = $link.text().trim();

				// Additional check: see if this looks like it's part of a row layout (3-across menu)
				// Try multiple parent selectors to find the row container
				let $parentRow = $td.closest('tr');
				if ($parentRow.length === 0) {
					$parentRow = $td.closest('.u-row');
				}
				if ($parentRow.length === 0) {
					$parentRow = $td.closest('[class*="row"]');
				}

				console.log(`[Pattern 6.5 Debug] Checking link "${linkText}"`);
				console.log(`[Pattern 6.5 Debug] Parent row found: ${$parentRow.length > 0 ? $parentRow.prop('tagName') || $parentRow.attr('class') : 'none'}`);

				// Look for similar cells in multiple ways
				let similarCellsInRow = $();

				// Method 1: Direct td siblings
				if ($parentRow.is('tr')) {
					similarCellsInRow = $parentRow.find('td').filter(function(this: any) {
						const $cell = $(this);
						const cellLinks = $cell.find('a');
						const cellText = getTextContent(this).trim();
						const cellImages = $cell.find('img').length;
						return cellLinks.length === 1 && !cellImages && cellText.length > 2 && cellText.length < 50;
					});
				}

				// Method 2: Look for .u-col elements (Unlayer structure)
				if (similarCellsInRow.length < 2) {
					similarCellsInRow = $parentRow.find('.u-col').filter(function(this: any) {
						const $cell = $(this);
						const cellLinks = $cell.find('a');
						const cellText = getTextContent(this).trim();
						const cellImages = $cell.find('img').length;
						return cellLinks.length === 1 && !cellImages && cellText.length > 2 && cellText.length < 50;
					});
				}

				// Method 3: Look for any div with similar structure
				if (similarCellsInRow.length < 2) {
					similarCellsInRow = $parentRow.find('div').filter(function(this: any) {
						const $cell = $(this);
						const cellLinks = $cell.find('a');
						const cellText = getTextContent(this).trim();
						const cellImages = $cell.find('img').length;
						// More lenient check for divs
						return cellLinks.length === 1 && !cellImages && cellText.length > 1 && cellText.length < 100;
					});
				}

				console.log(`[Pattern 6.5 Debug] Found ${similarCellsInRow.length} similar cells in row`);
				similarCellsInRow.each(function(this: any) {
					const cellText = getTextContent(this).trim();
					console.log(`[Pattern 6.5 Debug] - Cell text: "${cellText}"`);
				});

				// If we found 1+ similar cells (including current), this could be a menu
				// Lowered threshold since we're having detection issues
				if (similarCellsInRow.length >= 1 && linkText.length > 0) {
					console.log(`[Pattern 6.5] Converting menu link: "${linkText}" (${similarCellsInRow.length} items detected in row)`);

					// Find the deepest div that contains the link and use that as the editable content
					let $targetDiv = $link.closest('div');

					// If the link is directly in a div with center alignment, use that div
					if ($targetDiv.length > 0) {
						const divStyle = $targetDiv.attr('style') || '';
						if (divStyle.includes('text-align: center') || divStyle.includes('text-align:center')) {
							console.log(`[Pattern 6.5] Found centered div for link: ${linkText}`);
						} else {
							// Look for a parent div with center alignment
							$targetDiv = $link.closest('div[style*="text-align: center"], div[style*="text-align:center"]');
							if ($targetDiv.length === 0) {
								// Fallback: use the first div containing the link
								$targetDiv = $link.closest('div');
							}
						}
					}

					if ($targetDiv.length > 0) {
						console.log(`[Pattern 6.5] Target div found, converting to editable block`);
						ensureAlignCenter($td[0]);
						$td.attr('data-klaviyo-region', 'true');
						$td.attr('data-klaviyo-region-width-pixels', '600');

						const divAttributes = getAttributesString($targetDiv[0]);
						const divContent = $targetDiv.html();

						$td.html(`
							<div class="klaviyo-block klaviyo-text-block">
								<div${divAttributes}>${divContent}</div>
							</div>
						`);
					} else {
						console.log(`[Pattern 6.5] No target div found for link: ${linkText}`);
					}
				} else {
					console.log(`[Pattern 6.5] Not enough similar cells found (${similarCellsInRow.length}) for link: ${linkText}`);
				}
			}
		});

		// Pattern 7: Target linked images in complex table structures (like social media icons)
		// Only process linked images that haven't been handled by previous patterns
		$('a > img').each((_i: any, img: any) => {
			const $img = $(img);
			const $link = $img.parent();

			// Skip if this image is inside ANY klaviyo region (prevents nested processing)
			const $anyKlaviyoParent = $img.closest('[data-klaviyo-region="true"]');
			if ($anyKlaviyoParent.length > 0) return;

			// Find the containing td for this linked image
			const $containingTd = $img.closest('td');
			if ($containingTd.length === 0 || hasKlaviyoRegion($containingTd[0])) return;

			// Debug logging for this specific pattern
			const imgSrc = $img.attr('src') || 'no-src';
			const linkHref = $link.attr('href') || 'no-href';
			console.log(`[Pattern 7] Processing linked image: ${imgSrc.substring(0, 50)}... Link: ${linkHref}`);

			// Check if this td primarily contains this linked image
			const $allImages = $containingTd.find('img');
			const textContent = $containingTd.text().trim();
			const $otherSignificantContent = $containingTd.find('*').not('table, tr, td, img, a, br').filter(function(this: any) {
				const $this = $(this);
				const text = $this.text().trim();
				// Only consider it significant if it has substantial text content AND is not just containing the image/link
				return text.length > 5 && !$this.find('img, a').length;
			});

			console.log(`[Pattern 7] ${imgSrc.substring(0, 30)}: images=${$allImages.length}, textLen=${textContent.length}, otherContent=${$otherSignificantContent.length}`);

			// Make this more permissive - convert if it's primarily an image cell
			if ($allImages.length === 1 && $otherSignificantContent.length === 0 && textContent.length < 20) {
				console.log(`[Pattern 7] Converting linked image: ${imgSrc.substring(0, 50)}... Link: ${linkHref}`);
				ensureAlignCenter($containingTd[0]);
				$containingTd.attr('data-klaviyo-region', 'true');
				$containingTd.attr('data-klaviyo-region-width-pixels', getRegionWidth(img));

				const linkAttributes = getAttributesString($link[0]);
				// Check if this image is in a row layout (like 3 across footer)
				const inRowLayout = isInRowLayout(img);
				const imgAttributes = inRowLayout ?
					getRowLayoutImageAttributes(img) :
					getImageAttributesWithConstraints(img);

				$containingTd.html(`
					<div class="klaviyo-block klaviyo-image-block">
						<a${linkAttributes}>
							<img${imgAttributes}>
						</a>
					</div>
				`);
			}
		});

		// Pattern 8: Fallback for any remaining unprocessed images in table cells
		// This runs last and only processes images that haven't been converted yet
		$('img').each((_i: any, img: any) => {
			const $img = $(img);
			const $containingTd = $img.closest('td');

			// Skip if no containing td or if td already has klaviyo region
			if ($containingTd.length === 0 || hasKlaviyoRegion($containingTd[0])) return;

			// CRITICAL: Also skip if this image is inside ANY klaviyo region (nested case)
			const $anyKlaviyoParent = $img.closest('[data-klaviyo-region="true"]');
			if ($anyKlaviyoParent.length > 0) return;

			// Check if this td contains primarily this image and minimal other content
			const $allImages = $containingTd.find('img');
			const textContent = $containingTd.text().trim();
			const $significantElements = $containingTd.find('*').not('img, br, table, tr, td, a').filter(function(this: any) {
				const $this = $(this);
				return $this.text().trim().length > 3 && !$this.find('img').length;
			});

			// More permissive conditions for fallback - catch images that other patterns missed
			const hasMinimalText = textContent.length < 30; // Increased from 10
			const hasMinimalElements = $significantElements.length === 0;
			const isImagePrimary = $allImages.length === 1;

			console.log(`[Pattern 8] ${$img.attr('src')?.substring(0, 30) || 'no-src'}: images=${$allImages.length}, textLen=${textContent.length}, elements=${$significantElements.length}`);

			// Convert if this is primarily an image with minimal text/elements
			if (isImagePrimary && hasMinimalText && hasMinimalElements) {
				ensureAlignCenter($containingTd[0]);
				$containingTd.attr('data-klaviyo-region', 'true');
				$containingTd.attr('data-klaviyo-region-width-pixels', getRegionWidth(img));

				console.log(`[Pattern 8] Converting image: ${$img.attr('src')?.substring(0, 50) || 'no-src'}...`);

				// Check if this image is in a row layout (like 3 across footer)
				const inRowLayout = isInRowLayout(img);
				const imgAttributes = inRowLayout ?
					getRowLayoutImageAttributes(img) :
					getImageAttributesWithConstraints(img);
				const isLinked = $img.parent().is('a');

				if (isLinked) {
					const $link = $img.parent();
					const linkAttributes = getAttributesString($link[0]);
					console.log(`[Pattern 8] Converting as linked image${inRowLayout ? ' (row layout)' : ''}`);
					// Preserve the original table structure but make the image editable
					const $originalTable = $containingTd.find('table').first();
					if ($originalTable.length > 0) {
						// Find the td containing the image and replace just that content
						const $imageTd = $img.closest('td').not($containingTd);
						if ($imageTd.length > 0) {
							$imageTd.html(`
								<a${linkAttributes}>
									<img${imgAttributes}>
								</a>
							`);
						} else {
							// Fallback: replace the whole table structure
							$containingTd.html(`
								<div class="klaviyo-block klaviyo-image-block">
									<table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
										<tbody><tr>
											<td style="padding-right: 0px;padding-left: 0px;" align="center">
												<a${linkAttributes}>
													<img${imgAttributes}>
												</a>
											</td>
										</tr>
									</tbody></table>
								</div>
							`);
						}
					} else {
						// No table structure, use simple approach
						$containingTd.html(`
							<div class="klaviyo-block klaviyo-image-block">
								<a${linkAttributes}>
									<img${imgAttributes}>
								</a>
							</div>
						`);
					}
				} else {
					console.log(`[Pattern 8] Converting as standalone image${inRowLayout ? ' (row layout)' : ''}`);
					$containingTd.html(`
						<div class="klaviyo-block klaviyo-image-block">
							<img${imgAttributes}>
						</div>
					`);
				}
			}
		});

		// Debug logging - count converted regions after processing
		const convertedRegions = $('[data-klaviyo-region="true"]').length;
		const remainingImages = $('img').not('[data-klaviyo-region="true"] img').length;
		console.log(`[Hybrid Conversion] Conversion complete: ${convertedRegions} regions created, ${remainingImages} images not converted`);

		if (remainingImages > 0) {
			console.log('[Hybrid Conversion] Remaining unconverted images:', $('img').not('[data-klaviyo-region="true"] img').map(function(this: any) {
				const $img = $(this);
				const $td = $img.closest('td');
				return {
					src: $img.attr('src') || 'no-src',
					alt: $img.attr('alt') || 'no-alt',
					containingTdHtml: $td.length > 0 ? $td.html()?.substring(0, 200) + '...' : 'not in td'
				};
			}).get());
		}

		// Debug logging - count what was converted
		const finalRegions = $('[data-klaviyo-region="true"]').length;
		const textBlocks = $('.klaviyo-text-block').length;
		const imageBlocks = $('.klaviyo-image-block').length;
		console.log(`[Hybrid Conversion] Completed: ${finalRegions} regions, ${textBlocks} text blocks, ${imageBlocks} image blocks`);

		// Return the modified HTML
		return $.html();
	}

	async createKlaviyoCampaignAsync(
		taskId: number,
		orgId: number,
		htmlFromRequest?: string
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Check if we have HTML content - prioritize stored HTML, then request HTML
			let htmlContent = task.emailHtml || htmlFromRequest;

			if (!htmlContent) {
				throw new Error('No HTML content available. Please save the email design first.');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			// Get segment data - handle both Raleon and Klaviyo segments
			let segmentId = '';
			let segment = null;

			// Check if this campaign uses a Klaviyo segment
			const campaignSegments = await this.campaignSegmentRepository.find({
				where: {campaignId: campaign.id}
			});

			const klaviyoSegment = campaignSegments.find(cs => cs.segmentType === 'klaviyo' && cs.klaviyoSegmentId);

			if (klaviyoSegment) {
				// Use Klaviyo segment ID directly
				segmentId = klaviyoSegment.klaviyoSegmentId!;
				console.log('Using Klaviyo segment:', segmentId);
			} else {
				// Fall back to Raleon segment lookup
				segment = await this.organizationSegmentRepository.findOne({
					where: {
						orgId: plan.organizationId,
						name: campaign.targetSegment
					}
				});

				if (!segment || !segment.id || !segment.externalId) {
					console.log('Valid segment not found, using all subscribers');
					segmentId = '';
				} else {
					segmentId = segment.externalId.toString();
				}
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Converting to hybrid template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Convert HTML to hybrid template format (no AI processing)
			let processedHtml = this.convertToHybridTemplate(htmlContent);
			let explanation = "HTML converted to Klaviyo hybrid template format with editable text and image regions.";

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Extracting email metadata'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Create template using the processed HTML content
			const templateId = await this.klaviyoService.createTemplate(orgId, {
				name: `${campaign.name} Hybrid Template`,
				html: processedHtml,
				editorType: 'USER_DRAGGABLE'
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Create campaign and get both campaign ID and message ID
			const {campaignId, messageId} = await this.klaviyoService.createCampaign(orgId, {
				name: campaign.name,
				templateId: templateId,
				subject: subject,
				previewText: preview,
				listId: segmentId,
				sendTime: campaign.scheduledDate
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Assigning template to campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId,
				messageId,
				templateId
			});

			// Update task with Klaviyo IDs
			await this.taskRepository.updateById(taskId, {
				klaviyoTemplateId: templateId,
				klaviyoCampaignId: campaignId
			});

			// Update status to completed
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				data: {
					templateId,
					campaignId,
					messageId,
					hybridTemplate: true,
					explanation: explanation
				}
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

		} catch (error: any) {
			console.error('Failed to create Klaviyo campaign:', error);
			// Update status to failed
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});
			throw error;
		}
	}

	async createKlaviyoCampaignHtmlOnlyAsync(
		taskId: number,
		orgId: number,
		htmlFromRequest?: string
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Check if we have HTML content - prioritize stored HTML, then request HTML
			let htmlContent = task.emailHtml || htmlFromRequest;

			if (!htmlContent) {
				throw new Error('No HTML content available. Please save the email design first.');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			// Get segment data - handle both Raleon and Klaviyo segments
			let segmentId = '';
			let segment = null;

			// Check if this campaign uses a Klaviyo segment
			const campaignSegments = await this.campaignSegmentRepository.find({
				where: {campaignId: campaign.id}
			});

			const klaviyoSegment = campaignSegments.find(cs => cs.segmentType === 'klaviyo' && cs.klaviyoSegmentId);

			if (klaviyoSegment) {
				// Use Klaviyo segment ID directly
				segmentId = klaviyoSegment.klaviyoSegmentId!;
				console.log('Using Klaviyo segment:', segmentId);
			} else {
				// Fall back to Raleon segment lookup
				segment = await this.organizationSegmentRepository.findOne({
					where: {
						orgId: plan.organizationId,
						name: campaign.targetSegment
					}
				});

				if (!segment || !segment.id || !segment.externalId) {
					console.log('Valid segment not found, using all subscribers');
					segmentId = '';
				} else {
					segmentId = segment.externalId.toString();
				}
			}

			// Update status - skip AI processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Extracting email metadata (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo template (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Create template using the original HTML content (no AI processing)
			const templateId = await this.klaviyoService.createTemplate(orgId, {
				name: `${campaign.name} Template (HTML-only)`,
				html: htmlContent, // Use original HTML without AI processing
				editorType: 'CODE' // Use CODE editor for HTML-only templates
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Create campaign and get both campaign ID and message ID
			const {campaignId, messageId} = await this.klaviyoService.createCampaign(orgId, {
				name: campaign.name,
				templateId: templateId,
				subject: subject,
				previewText: preview,
				listId: segmentId,
				sendTime: campaign.scheduledDate
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Assigning template to campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId,
				messageId,
				templateId
			});

			// Update task with both template and campaign IDs
			await this.taskRepository.updateById(taskId, {
				klaviyoTemplateId: templateId,
				klaviyoCampaignId: campaignId
			});

			// Mark as completed
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'Campaign creation completed (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

		} catch (error: any) {
			console.error('Failed to create Klaviyo campaign (HTML-only):', error);
			// Update status to failed
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});
			throw error;
		}
	}

	async resyncKlaviyoCampaignAsync(
		taskId: number,
		orgId: number
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (!task.emailHtml) {
				throw new Error('No HTML content available for resync');
			}

			// Check if we have Klaviyo template and campaign IDs stored
			const contentStep = task.taskSteps?.find(step => step.taskTypeId === 3); // ID 3 is for email content
			if (!contentStep?.data) {
				throw new Error('No Klaviyo campaign data found');
			}

			// const klaviyoData = JSON.parse(contentStep.data);
			if (!task.klaviyoTemplateId || !task.klaviyoCampaignId) {
				throw new Error('Missing Klaviyo template or campaign ID');
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Converting to hybrid template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Convert HTML to hybrid template format (no AI processing)
			let processedHtml = this.convertToHybridTemplate(task.emailHtml);
			let explanation = "HTML converted to Klaviyo hybrid template format with editable text and image regions.";

			console.log('Using hybrid template conversion for resync (no AI processing)');

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Update the template
			await this.klaviyoService.updateTemplate(orgId, task.klaviyoTemplateId, {
				html: processedHtml
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Determine send time - if the scheduled date is in the past, bump it to tomorrow
			let sendTime = campaign.scheduledDate;
			const scheduledDate = new Date(campaign.scheduledDate);
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (scheduledDate < today) {
				// If the date is in the past, set it to tomorrow at the same time
				const tomorrow = new Date();
				tomorrow.setDate(tomorrow.getDate() + 1);
				// Keep the original time if it was set, otherwise use current time
				const originalTime = new Date(campaign.scheduledDate);
				if (!isNaN(originalTime.getTime())) {
					tomorrow.setHours(originalTime.getHours(), originalTime.getMinutes(), originalTime.getSeconds());
				}
				sendTime = tomorrow.toISOString().split('T')[0]; // Format as YYYY-MM-DD

				// Update the campaign's scheduled date in our database to match
				await this.plannerCampaignRepository.updateById(campaign.id, {
					scheduledDate: sendTime
				});
			}

			// Update the campaign
			const data = await this.klaviyoService.updateCampaign(orgId, task.klaviyoCampaignId, {
				name: campaign.name,
				subject,
				previewText: preview,
				sendTime: sendTime
			});

			const messageId = data.data.relationships['campaign-messages'].data[0].id;
			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId: task.klaviyoCampaignId,
				messageId,
				templateId: task.klaviyoTemplateId
			});

			// Update status to complete
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'Resync completed successfully',
				data: {
					templateId: task.klaviyoTemplateId,
					campaignId: task.klaviyoCampaignId,
					messageId,
					htmlProcessed: true,
					explanation: explanation
				},
				startTime: new Date().toISOString()
			}, {
				taskId: taskId
			});

		} catch (error: any) {
			console.error('Error in resyncKlaviyoCampaignAsync:', error);
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message,
				startTime: new Date().toISOString()
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});
			throw error;
		}
	}

	async resyncKlaviyoCampaignHtmlOnlyAsync(
		taskId: number,
		orgId: number
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data (HTML-only resync)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);

			if (!task.emailHtml) {
				throw new Error('No HTML content available for resync');
			}

			// Check if we have Klaviyo campaign ID (template ID might be missing for older campaigns)
			if (!task.klaviyoCampaignId) {
				throw new Error('Missing Klaviyo campaign ID');
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo template (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Update or create template with original HTML (no AI processing)
			let templateId = task.klaviyoTemplateId;
			if (!templateId) {
				// Create a new template if one doesn't exist
				const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
				templateId = await this.klaviyoService.createTemplate(orgId, {
					name: `${campaign.name} Template (HTML-only)`,
					html: task.emailHtml,
					editorType: 'CODE'
				});
				// Update task with the new template ID
				await this.taskRepository.updateById(taskId, {
					klaviyoTemplateId: templateId
				});
			} else {
				// Update existing template
				await this.klaviyoService.updateTemplate(orgId, templateId, {
					html: task.emailHtml // Use original HTML without AI processing
				});
			}

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Determine send time - if the scheduled date is in the past, bump it to tomorrow
			let sendTime = campaign.scheduledDate;
			const scheduledDate = new Date(campaign.scheduledDate);
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (scheduledDate < today) {
				// If the date is in the past, set it to tomorrow at the same time
				const tomorrow = new Date();
				tomorrow.setDate(tomorrow.getDate() + 1);
				// Keep the original time if it was set, otherwise use current time
				const originalTime = new Date(campaign.scheduledDate);
				if (!isNaN(originalTime.getTime())) {
					tomorrow.setHours(originalTime.getHours(), originalTime.getMinutes(), originalTime.getSeconds());
				}
				sendTime = tomorrow.toISOString().split('T')[0]; // Format as YYYY-MM-DD

				// Update the campaign's scheduled date in our database to match
				await this.plannerCampaignRepository.updateById(campaign.id, {
					scheduledDate: sendTime
				});
			}

			// Update the campaign
			const data = await this.klaviyoService.updateCampaign(orgId, task.klaviyoCampaignId, {
				name: campaign.name,
				subject,
				previewText: preview,
				sendTime: sendTime
			});

			const messageId = data.data.relationships['campaign-messages'].data[0].id;
			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId: task.klaviyoCampaignId,
				messageId,
				templateId: templateId
			});

			// Update status to complete
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'HTML-only resync completed successfully',
				data: {
					templateId: templateId,
					campaignId: task.klaviyoCampaignId,
					messageId,
					htmlProcessed: false, // No AI processing for HTML-only
					explanation: 'HTML was updated directly without AI processing'
				}
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

		} catch (error: any) {
			console.error('Error in resyncKlaviyoCampaignHtmlOnlyAsync:', error);
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});
			throw error;
		}
	}

	async generateEmailContentAsync(taskId: number, orgId: number): Promise<void> {
		try {
			const task = await this.taskRepository.findById(taskId, {
				include: [{relation: 'taskSteps'}]
			});
			if (!task) {
				throw new Error('Task not found');
			}
			console.log('Generating email content for task:', taskId);
			const emailDesign = await this.createUnlayerEmail(task);

			// Update generation record with success
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				design: emailDesign
			}, {
				taskId: taskId,
				operationType: 'email'
			});

		} catch (error: any) {
			console.error('Failed to generate email content:', error);
			// Update generation record with failure
			await this.emailGenerationRepository.updateAll({
				status: 'failed'
			}, {
				taskId: taskId,
				operationType: 'email'
			});
		}
	}
}
